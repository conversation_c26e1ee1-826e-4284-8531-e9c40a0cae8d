document.fonts.ready.then(function () {
  $(document).on("initPage", function () {
    setAnchorPoints();
    setHeaderBackground();
  });
});

function setAnchorPoints() {
  $(".anchorMenu").each(function () {
    var $anchorMenu = $(this);
    $($anchorMenu).html("");
    $("[data-anchor]").each(function () {
      const $section = $(this);
      const anchorName = $section.data("anchor");

      const $menuItem = $(`<a href="#${anchorName}" class="menu-item">${anchorName}</a>`);
      $anchorMenu.append($menuItem);
    });

    $anchorMenu.on("click touchend", ".menu-item", function (event) {
      const targetAnchor = $(this).attr("href");
      if (!targetAnchor) return;

      event.preventDefault();
      const targetID = targetAnchor.slice(1);
      const $targetSection = $(`[data-anchor="${targetID}"]`);

      if ($targetSection.length) {
        scroller.scrollTo($targetSection[0], {
          offset: 0,
          duration: 1.2,
          easing: (t) => 1 - Math.pow(1 - t, 4),
        });
      }
    });
  });

  $(document).on("click touchend", ".button", function (event) {
    const targetAnchor = $(this).attr("href");
    if (!targetAnchor) return;

    event.preventDefault();
    const targetID = targetAnchor.slice(1);
    const $targetSection = $(`[data-anchor="${targetID}"]`);

    if ($targetSection.length) {
      scroller.scrollTo($targetSection[0], {
        offset: 0,
        duration: 1.2,
        easing: (t) => 1 - Math.pow(1 - t, 4),
      });
    }
  });
}

function setHeaderBackground() {
  // Clear any existing ScrollTriggers for header background
  ScrollTrigger.getAll().forEach(trigger => {
    if (trigger.vars && trigger.vars.id === 'headerBackground') {
      trigger.kill();
    }
  });

  const $header = $("header");
  const $sections = $("section");

  // Check if first section has whiteBackground class on page load
  const $firstSection = $sections.first();
  if ($firstSection.hasClass('whiteBackground')) {
    $header.addClass('showBackground');
  } else {
    $header.removeClass('showBackground');
  }

  // Create ScrollTrigger for each section to monitor which one is in view
  $sections.each(function(index) {
    const section = this;
    const $section = $(section);

    ScrollTrigger.create({
      id: 'headerBackground',
      trigger: section,
      start: "top center",
      end: "bottom center",
      onEnter: () => {
        if ($section.hasClass('whiteBackground')) {
          $header.addClass('showBackground');
        } else {
          $header.removeClass('showBackground');
        }
      },
      onEnterBack: () => {
        if ($section.hasClass('whiteBackground')) {
          $header.addClass('showBackground');
        } else {
          $header.removeClass('showBackground');
        }
      }
    });
  });
}
