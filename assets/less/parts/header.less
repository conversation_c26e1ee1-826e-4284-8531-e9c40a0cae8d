@import '../vw_values.less';
@import '../constants.less'; 
header {
    position: fixed;
    padding-top: @vw22;
    top: 0;
    width: 100%;
    left: 0;
    z-index: 99;
    &.showBackground {
      &:before {
        opacity: 1;
      }
    }
    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,.4);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      z-index: -1;
      opacity: 0;
      .transitionMore(opacity, .3s, 0s, ease-out);
    }
    > .background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: @vw100 * 3;
      pointer-events: none;
      z-index: -1;
      backdrop-filter: blur(@vw50); // Pas de blur-waarde aan naar wens
      -webkit-backdrop-filter: blur(10px); // Voor Safari-ondersteuning
      background: rgba(0,0,0,0); // Zorg ervoor dat de achtergrond deels transparant is
      -webkit-mask-image: linear-gradient(rgba(0,0,0,1), rgba(0,0,0,0));
      mask-image: linear-gradient(rgba(0,0,0,1), rgba(0,0,0,0));
    }
    .innerMenu {
        display: inline-block;
        vertical-align: middle;
        list-style: none;
        li {
          display: inline-block;
          a {
              display: inline-block;
              vertical-align: middle;
              padding: @vw10 @vw20;
              cursor: pointer;
              font-family: 'ApexMk2-Regular', Arial, sans-serif;
              font-weight: 200;;
              color: @hardWhite;
              text-decoration: none;
              .transitionMore(opacity, .3s);
              &:first-child {
                padding-left: 0;
              }
              &:not(:last-of-type) {
                  margin-right: @vw22;
              }
              &:hover {
                opacity: .5;
              }
          }
        }
    }
    .col {
        display: inline-block;
        vertical-align: middle;
        position: relative;
        width: calc(100% ~"-" @vw106 ~"-" @vw16);
        &.small {
          width: @vw106 + @vw16;
        }
        &:last-child {
          display: inline-flex;
          padding-left: (@vw106 * 3) + (@vw16 * 3);
        }
        .logo {
            display: inline-block;
            width: auto;
            vertical-align: middle;
            height: @vw17;
            img {
              height: 100%;
              width: auto;
              display: block;
            }
        }
        .menu-primary-menu-container {
          display: inline-block;
          vertical-align: middle;
        }
        .button {
            display: inline-block;
            margin-left: @vw30;
            vertical-align: middle;
            &:hover {
              .innerText {
                padding: 0 @vw20;
              }
              .hamburger {
                .border {
                  &:after {
                    transform: scaleX(1);
                    .transitionMore(transform, .45s, .15s, cubic-bezier(0.34, 1.56, 0.64, 1));
                    .stagger(3, .08s, .15s);
                  }
                }
              }
            }
            .innerText {
              padding: 0 @vw20;
              text-decoration: none;
              color: @hardWhite;
            }
            .arrows {
              position: relative;
            }
        }
    }
}

.hamburger {
  cursor: pointer;
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  .transform(translate(-50%, -50%));
  height: @vw14;
  width: @vw16;
  .border {
    position: absolute;
    display: block;
    height: 2px;
    width: 100%;
    border-radius: @vw2;
    background: @secondaryColor;
    .transition(.3s);
    &:after {
      content: '';
      position: absolute;
      background: @hardWhite;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      .transform(scaleX(0));
    }
    &:first-child {
      top: 0;
    }
    &:nth-child(2) {
      top: 50%;
      transform: translateY(-50%);
    }
    &:nth-child(3) {
      bottom: 0;
      top: auto;
    }
  }
}

.blurredOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 98;
  opacity: 0;
  pointer-events: none;
  background: rgba(255, 255, 255, 0.13);
  backdrop-filter: blur(@vw50);
  -webkit-backdrop-filter: blur(10px);
  .transitionMore(opacity, .3s, .6s, ease-out);
  &.active {
    opacity: 1;
    pointer-events: all;
    .transitionMore(opacity, .3s, .15s, ease-out);
  }
}

#menu {
  display: inline-block;
  padding: @vw20;
  cursor: pointer;
  padding: @vw100 + @vw100 + @vw10 @vw36;
  height: 100vh;
  .border-radius(0, 0, @vw6, @vw6);
  position: fixed;
  width: 33.3333vw;
  right: 0; 
  top: 0;
  background: @hardWhite;
  color: @almostWhite;
  font-size: @vw21;
  z-index: 99;
  transform-origin: right;
  .transform(scaleX(0));
  .transitionMore(transform, .3s, .45s, ease-out);
  .background {
    height: 0;
    opacity: 0;
    position: absolute;
    right: 0;
    top: 0;
    background: @grey;
    .transitionMore(opacity, .3s, 0s, ease-in);
  }
  &.active {
    color: @almostBlack;
    .transform(scaleX(1));
    .transitionMore(transform, .6s, 0s, cubic-bezier(0.16, 1, 0.3, 1));
    .background {
      opacity: 1;
    }
    .innerContent {
      .divider {
        .transform(scaleX(1));
        .transitionMore(transform, .45s, .75s, cubic-bezier(0.83, 0, 0.17, 1));
      }
    }
    .contactLinks, .socials {
      .transform(translateY(0));
      opacity: 1;
      -webkit-transition: transform 0.3s 0.75s ease-out, opacity 0.3s 0.75s ease-out;
      transition: transform 0.3s 0.75s ease-out, opacity 0.3s 0.75s ease-out;
    }
  }
  div, span {
    cursor: pointer;
    display: inline-block;
  }
  .hamburger {
      top: @vw20;
      right: @vw20;
      .transform(translate3d(0,0,0));
      z-index: 1;
      left: auto;
      .border {
        background: @secondaryColor;
        &:nth-child(1) {
          .transform(translateY(@vw6) rotate(-45deg));
        }
        &:nth-child(2) {
          width: 0%;
        }
        &:nth-child(3) {
          .transform(translateY(-@vw6) rotate(45deg));
        }
      }
    }
  .innerContent {
    width: (@vw106 * 3) + (@vw16 * 2);
    li {
      line-height: 1.5;
      a {
        color: @primaryColor;
        font-size: @vw30;
        font-family: "ApexMk2-Regular", Arial, sans-serif;
        font-weight: 200;
        text-decoration: none;
        .transitionMore(color, .15s);
        &:hover {
          color: @secondaryColorLight;
        }
      }
    }
    .divider {
      margin: @vw30 0;
      width: 100%;
      background: @primaryColor;
      height: 1px;
      transform-origin: left;
      .transform(scaleX(0));
      .transitionMore(transform, .3s, 0s, ease-out);
    }
  }
  .logo {
    position: absolute;
    top: @vw17;
    left: @vw36;
    height: @vw17;
    width: auto;
    .transitionMore(opacity, .3s);
    &:hover {
      opacity: .5;
    }
    img {
      height: 100%;
      width: auto;
      display: block;
    }
  }
  .socials {
    margin: @vw30 0;
    .social {
      display: inline-block;
      height: @vw46;
      width: @vw46;
      cursor: pointer;
      .rounded(50%);
      background-color: rgba(212, 208, 237, 0.1);
      text-decoration: none;
      color: @secondaryColorLight;
      line-height: @vw49;
      -webkit-transition: color .3s, background-color .3s;
      transition: color .3s, background-color .3s;
      text-align: center;
      font-size: @vw22;
      &:not(:last-child) {
        margin-right: @vw20;
      }
      &:hover {
        background-color: rgba(212, 208, 237, 0.2);
        color: @secondaryColor;
      }
      i {
        pointer-events: none;
      }
    }
  }
  .contactLinks, .socials {
    .transform(translateY(@vw20));
    opacity: 0;
    -webkit-transition: transform 0.3s 0s ease-out, opacity 0.3s 0s ease-out;
    transition: transform 0.3s 0s ease-out, opacity 0.3s 0s ease-out;
  }
  .contactLink {
    cursor: pointer;
    display: table;
    font-family: "ApexMk2-Regular", Arial, sans-serif;
    font-weight: 200;
    font-size: @vw20;
    color: @primaryColor;
    text-decoration: none;
    .transitionMore(color, .15s);
    i {
      cursor: pointer;
    }
    &:hover {
      color: @secondaryColorLight;
    }
    &:not(:last-child) {
      margin-bottom: @vw12;
    }
  }
  .innerContent {
    &.showContent {
      ul {
        &.hover {
          li {
            a {
              opacity: .2;
            }
            &.active {
              a {
                opacity: 1;
              }
            }
          }
        }
        li {
          visibility: visible;
          opacity: 1;
          transform: translateY(0);
          .stagger(20, .15s, .15s);
        }
      }
    }
    > div {
      display: block;
    }
    .menu-primary-menu-container {
      ul {
        li {
          font-size: @vw24;
          line-height: 1.4;
          font-family: "Roboto", sans-serif;
          font-weight: 500;
        }
      }
    }
    ul {
      list-style: none;
      li {
        font-family: "Roboto", sans-serif;
        font-size: @vw21;
        font-weight: 600;
        padding-bottom: @vw10;
        line-height: @vw31;
        text-transform: uppercase;
        visibility: hidden;
        position: relative;
        opacity: 0;
        transform: translateY(@vw20);
        .transition(.3s);
        &:last-child {
          padding-bottom: 0;
        }
        a {
          cursor: pointer;
          color: @almostBlack;
          text-decoration: none;
          .transition(.15s);
        }
      }
    }
  }
}