header {
  position: fixed;
  padding-top: 1.273vw;
  top: 0;
  width: 100%;
  left: 0;
  z-index: 99;
}
header.showBackground:before {
  opacity: 1;
}
header:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: -1;
  opacity: 0;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
header > .background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 17.361vw;
  pointer-events: none;
  z-index: -1;
  backdrop-filter: blur(2.894vw);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0);
  -webkit-mask-image: linear-gradient(#000000, rgba(0, 0, 0, 0));
  mask-image: linear-gradient(#000000, rgba(0, 0, 0, 0));
}
header .innerMenu {
  display: inline-block;
  vertical-align: middle;
  list-style: none;
}
header .innerMenu li {
  display: inline-block;
}
header .innerMenu li a {
  display: inline-block;
  vertical-align: middle;
  padding: 0.579vw 1.157vw;
  cursor: pointer;
  font-family: 'ApexMk2-Regular', Arial, sans-serif;
  font-weight: 200;
  color: #FFFFFF;
  text-decoration: none;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
header .innerMenu li a:first-child {
  padding-left: 0;
}
header .innerMenu li a:not(:last-of-type) {
  margin-right: 1.273vw;
}
header .innerMenu li a:hover {
  opacity: 0.5;
}
header .col {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  width: calc(100% - 6.625vw - 0.926vw);
}
header .col.small {
  width: 7.551vw;
}
header .col:last-child {
  display: inline-flex;
  padding-left: 22.653vw;
}
header .col .logo {
  display: inline-block;
  width: auto;
  vertical-align: middle;
  height: 0.984vw;
}
header .col .logo img {
  height: 100%;
  width: auto;
  display: block;
}
header .col .menu-primary-menu-container {
  display: inline-block;
  vertical-align: middle;
}
header .col .button {
  display: inline-block;
  margin-left: 1.736vw;
  vertical-align: middle;
}
header .col .button:hover .innerText {
  padding: 0 1.157vw;
}
header .col .button:hover .hamburger .border:after {
  transform: scaleX(1);
  -webkit-transition: transform 0.45s 0.15s cubic-bezier(0.34, 1.56, 0.64, 1);
  -moz-transition: transform 0.45s 0.15s cubic-bezier(0.34, 1.56, 0.64, 1);
  -o-transition: transform 0.45s 0.15s cubic-bezier(0.34, 1.56, 0.64, 1);
  transition: transform 0.45s 0.15s cubic-bezier(0.34, 1.56, 0.64, 1);
}
header .col .button:hover .hamburger .border:after:nth-child(3) {
  transition-delay: 0.39s;
}
header .col .button:hover .hamburger .border:after:nth-child(2) {
  transition-delay: 0.31s;
}
header .col .button:hover .hamburger .border:after:nth-child(1) {
  transition-delay: 0.23s;
}
header .col .button .innerText {
  padding: 0 1.157vw;
  text-decoration: none;
  color: #FFFFFF;
}
header .col .button .arrows {
  position: relative;
}
.hamburger {
  cursor: pointer;
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  height: 0.81vw;
  width: 0.926vw;
}
.hamburger .border {
  position: absolute;
  display: block;
  height: 2px;
  width: 100%;
  border-radius: 0.116vw;
  background: #5A51A3;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.hamburger .border:after {
  content: '';
  position: absolute;
  background: #FFFFFF;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-transform: scaleX(0);
  -moz-transform: scaleX(0);
  -o-transform: scaleX(0);
  -ms-transform: scaleX(0);
  transform: scaleX(0);
}
.hamburger .border:first-child {
  top: 0;
}
.hamburger .border:nth-child(2) {
  top: 50%;
  transform: translateY(-50%);
}
.hamburger .border:nth-child(3) {
  bottom: 0;
  top: auto;
}
.blurredOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 98;
  opacity: 0;
  pointer-events: none;
  background: rgba(255, 255, 255, 0.13);
  backdrop-filter: blur(2.894vw);
  -webkit-backdrop-filter: blur(10px);
  -webkit-transition: opacity 0.3s 0.6s ease-out;
  -moz-transition: opacity 0.3s 0.6s ease-out;
  -o-transition: opacity 0.3s 0.6s ease-out;
  transition: opacity 0.3s 0.6s ease-out;
}
.blurredOverlay.active {
  opacity: 1;
  pointer-events: all;
  -webkit-transition: opacity 0.3s 0.15s ease-out;
  -moz-transition: opacity 0.3s 0.15s ease-out;
  -o-transition: opacity 0.3s 0.15s ease-out;
  transition: opacity 0.3s 0.15s ease-out;
}
#menu {
  display: inline-block;
  padding: 1.157vw;
  cursor: pointer;
  padding: 12.153vw 2.083vw;
  height: 100vh;
  -webkit-border-top-right-radius: 0;
  -webkit-border-bottom-right-radius: 0;
  -webkit-border-bottom-left-radius: 0.347vw;
  -webkit-border-top-left-radius: 0.347vw;
  -moz-border-radius-topright: 0;
  -moz-border-radius-bottomright: 0;
  -moz-border-radius-bottomleft: 0.347vw;
  -moz-border-radius-topleft: 0.347vw;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0.347vw;
  border-top-left-radius: 0.347vw;
  -moz-background-clip: padding-box;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  position: fixed;
  width: 33.3333vw;
  right: 0;
  top: 0;
  background: #FFFFFF;
  color: #C2C2B5;
  font-size: 1.215vw;
  z-index: 99;
  transform-origin: right;
  -webkit-transform: scaleX(0);
  -moz-transform: scaleX(0);
  -o-transform: scaleX(0);
  -ms-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transition: transform 0.3s 0.45s ease-out;
  -moz-transition: transform 0.3s 0.45s ease-out;
  -o-transition: transform 0.3s 0.45s ease-out;
  transition: transform 0.3s 0.45s ease-out;
}
#menu .background {
  height: 0;
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0;
  background: #C2C2B5;
  -webkit-transition: opacity 0.3s 0s ease-in;
  -moz-transition: opacity 0.3s 0s ease-in;
  -o-transition: opacity 0.3s 0s ease-in;
  transition: opacity 0.3s 0s ease-in;
}
#menu.active {
  color: #191919;
  -webkit-transform: scaleX(1);
  -moz-transform: scaleX(1);
  -o-transform: scaleX(1);
  -ms-transform: scaleX(1);
  transform: scaleX(1);
  -webkit-transition: transform 0.6s 0s cubic-bezier(0.16, 1, 0.3, 1);
  -moz-transition: transform 0.6s 0s cubic-bezier(0.16, 1, 0.3, 1);
  -o-transition: transform 0.6s 0s cubic-bezier(0.16, 1, 0.3, 1);
  transition: transform 0.6s 0s cubic-bezier(0.16, 1, 0.3, 1);
}
#menu.active .background {
  opacity: 1;
}
#menu.active .innerContent .divider {
  -webkit-transform: scaleX(1);
  -moz-transform: scaleX(1);
  -o-transform: scaleX(1);
  -ms-transform: scaleX(1);
  transform: scaleX(1);
  -webkit-transition: transform 0.45s 0.75s cubic-bezier(0.83, 0, 0.17, 1);
  -moz-transition: transform 0.45s 0.75s cubic-bezier(0.83, 0, 0.17, 1);
  -o-transition: transform 0.45s 0.75s cubic-bezier(0.83, 0, 0.17, 1);
  transition: transform 0.45s 0.75s cubic-bezier(0.83, 0, 0.17, 1);
}
#menu.active .contactLinks,
#menu.active .socials {
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -o-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
  -webkit-transition: transform 0.3s 0.75s ease-out, opacity 0.3s 0.75s ease-out;
  transition: transform 0.3s 0.75s ease-out, opacity 0.3s 0.75s ease-out;
}
#menu div,
#menu span {
  cursor: pointer;
  display: inline-block;
}
#menu .hamburger {
  top: 1.157vw;
  right: 1.157vw;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  z-index: 1;
  left: auto;
}
#menu .hamburger .border {
  background: #5A51A3;
}
#menu .hamburger .border:nth-child(1) {
  -webkit-transform: translateY(0.347vw) rotate(-45deg);
  -moz-transform: translateY(0.347vw) rotate(-45deg);
  -o-transform: translateY(0.347vw) rotate(-45deg);
  -ms-transform: translateY(0.347vw) rotate(-45deg);
  transform: translateY(0.347vw) rotate(-45deg);
}
#menu .hamburger .border:nth-child(2) {
  width: 0%;
}
#menu .hamburger .border:nth-child(3) {
  -webkit-transform: translateY(-0.347vw) rotate(45deg);
  -moz-transform: translateY(-0.347vw) rotate(45deg);
  -o-transform: translateY(-0.347vw) rotate(45deg);
  -ms-transform: translateY(-0.347vw) rotate(45deg);
  transform: translateY(-0.347vw) rotate(45deg);
}
#menu .innerContent {
  width: 21.727vw;
}
#menu .innerContent li {
  line-height: 1.5;
}
#menu .innerContent li a {
  color: #080036;
  font-size: 1.736vw;
  font-family: "ApexMk2-Regular", Arial, sans-serif;
  font-weight: 200;
  text-decoration: none;
  -webkit-transition: color 0.15s 0s ease-out;
  -moz-transition: color 0.15s 0s ease-out;
  -o-transition: color 0.15s 0s ease-out;
  transition: color 0.15s 0s ease-out;
}
#menu .innerContent li a:hover {
  color: #D4D0ED;
}
#menu .innerContent .divider {
  margin: 1.736vw 0;
  width: 100%;
  background: #080036;
  height: 1px;
  transform-origin: left;
  -webkit-transform: scaleX(0);
  -moz-transform: scaleX(0);
  -o-transform: scaleX(0);
  -ms-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transition: transform 0.3s 0s ease-out;
  -moz-transition: transform 0.3s 0s ease-out;
  -o-transition: transform 0.3s 0s ease-out;
  transition: transform 0.3s 0s ease-out;
}
#menu .logo {
  position: absolute;
  top: 0.984vw;
  left: 2.083vw;
  height: 0.984vw;
  width: auto;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
#menu .logo:hover {
  opacity: 0.5;
}
#menu .logo img {
  height: 100%;
  width: auto;
  display: block;
}
#menu .socials {
  margin: 1.736vw 0;
}
#menu .socials .social {
  display: inline-block;
  height: 2.662vw;
  width: 2.662vw;
  cursor: pointer;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  background-color: rgba(212, 208, 237, 0.1);
  text-decoration: none;
  color: #D4D0ED;
  line-height: 2.836vw;
  -webkit-transition: color 0.3s, background-color 0.3s;
  transition: color 0.3s, background-color 0.3s;
  text-align: center;
  font-size: 1.273vw;
}
#menu .socials .social:not(:last-child) {
  margin-right: 1.157vw;
}
#menu .socials .social:hover {
  background-color: rgba(212, 208, 237, 0.2);
  color: #5A51A3;
}
#menu .socials .social i {
  pointer-events: none;
}
#menu .contactLinks,
#menu .socials {
  -webkit-transform: translateY(1.157vw);
  -moz-transform: translateY(1.157vw);
  -o-transform: translateY(1.157vw);
  -ms-transform: translateY(1.157vw);
  transform: translateY(1.157vw);
  opacity: 0;
  -webkit-transition: transform 0.3s 0s ease-out, opacity 0.3s 0s ease-out;
  transition: transform 0.3s 0s ease-out, opacity 0.3s 0s ease-out;
}
#menu .contactLink {
  cursor: pointer;
  display: table;
  font-family: "ApexMk2-Regular", Arial, sans-serif;
  font-weight: 200;
  font-size: 1.157vw;
  color: #080036;
  text-decoration: none;
  -webkit-transition: color 0.15s 0s ease-out;
  -moz-transition: color 0.15s 0s ease-out;
  -o-transition: color 0.15s 0s ease-out;
  transition: color 0.15s 0s ease-out;
}
#menu .contactLink i {
  cursor: pointer;
}
#menu .contactLink:hover {
  color: #D4D0ED;
}
#menu .contactLink:not(:last-child) {
  margin-bottom: 0.694vw;
}
#menu .innerContent.showContent ul.hover li a {
  opacity: 0.2;
}
#menu .innerContent.showContent ul.hover li.active a {
  opacity: 1;
}
#menu .innerContent.showContent ul li {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}
#menu .innerContent.showContent ul li:nth-child(20) {
  transition-delay: 3.15s;
}
#menu .innerContent.showContent ul li:nth-child(19) {
  transition-delay: 3s;
}
#menu .innerContent.showContent ul li:nth-child(18) {
  transition-delay: 2.85s;
}
#menu .innerContent.showContent ul li:nth-child(17) {
  transition-delay: 2.7s;
}
#menu .innerContent.showContent ul li:nth-child(16) {
  transition-delay: 2.55s;
}
#menu .innerContent.showContent ul li:nth-child(15) {
  transition-delay: 2.4s;
}
#menu .innerContent.showContent ul li:nth-child(14) {
  transition-delay: 2.25s;
}
#menu .innerContent.showContent ul li:nth-child(13) {
  transition-delay: 2.1s;
}
#menu .innerContent.showContent ul li:nth-child(12) {
  transition-delay: 1.95s;
}
#menu .innerContent.showContent ul li:nth-child(11) {
  transition-delay: 1.8s;
}
#menu .innerContent.showContent ul li:nth-child(10) {
  transition-delay: 1.65s;
}
#menu .innerContent.showContent ul li:nth-child(9) {
  transition-delay: 1.5s;
}
#menu .innerContent.showContent ul li:nth-child(8) {
  transition-delay: 1.35s;
}
#menu .innerContent.showContent ul li:nth-child(7) {
  transition-delay: 1.2s;
}
#menu .innerContent.showContent ul li:nth-child(6) {
  transition-delay: 1.05s;
}
#menu .innerContent.showContent ul li:nth-child(5) {
  transition-delay: 0.9s;
}
#menu .innerContent.showContent ul li:nth-child(4) {
  transition-delay: 0.75s;
}
#menu .innerContent.showContent ul li:nth-child(3) {
  transition-delay: 0.6s;
}
#menu .innerContent.showContent ul li:nth-child(2) {
  transition-delay: 0.45s;
}
#menu .innerContent.showContent ul li:nth-child(1) {
  transition-delay: 0.3s;
}
#menu .innerContent > div {
  display: block;
}
#menu .innerContent .menu-primary-menu-container ul li {
  font-size: 1.389vw;
  line-height: 1.4;
  font-family: "Roboto", sans-serif;
  font-weight: 500;
}
#menu .innerContent ul {
  list-style: none;
}
#menu .innerContent ul li {
  font-family: "Roboto", sans-serif;
  font-size: 1.215vw;
  font-weight: 600;
  padding-bottom: 0.579vw;
  line-height: 1.794vw;
  text-transform: uppercase;
  visibility: hidden;
  position: relative;
  opacity: 0;
  transform: translateY(1.157vw);
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
#menu .innerContent ul li:last-child {
  padding-bottom: 0;
}
#menu .innerContent ul li a {
  cursor: pointer;
  color: #191919;
  text-decoration: none;
  -webkit-transition: all 0.15s ease-out;
  -moz-transition: all 0.15s ease-out;
  -o-transition: all 0.15s ease-out;
  transition: all 0.15s ease-out;
}
